<template>
	<checkbox
		class="custom-checkbox"
		:checked="checked"
		@change="handleChange"
		:color="checkedColor"
	/>
</template>

<script>
export default {
	name: 'Check<PERSON><PERSON>',
	props: {
		checked: {
			type: Boolean,
			default: false
		},
		checkedColor: {
			type: String,
			default: '#007AFF'
		}
	},
	methods: {
		handleChange(e) {
			this.$emit('change', e.detail.value);
		}
	}
}
</script>

<style lang="scss" scoped>
.custom-checkbox {
	transform: scale(0.8);
}
</style>
